package com.lanhu.lims.gateway.admin.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title DataValidation
 * @package com.lanhu.lims.gateway.admin.validation.annotation
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/19 16:38
 * @version 0.0.1
 *********************************/
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataValidation {

}
