package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description: ${DESCRIPTION}
 * @author: huangzheng
 * @date: 2025/6/18 14:02
 */
@ApiModel(description="t_business_flow_module")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_business_flow_module")
public class BusinessFlowModule {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键id")
    private Long id;

    /**
     * 模块或类型名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="模块或类型名称")
    private String name;

    /**
     * 排序
     */
    @TableField(value = "sort_by")
    @ApiModelProperty(value="排序")
    private Integer sortBy;

    /**
     * 模块的pid为0，类型的pid为响应模块的id
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value="模块的pid为0，类型的pid为响应模块的id")
    private Long parentId;

    /**
     * 创建人id
     */
    @TableField(value = "creat_by")
    @ApiModelProperty(value="创建人id")
    private Long creatBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value="创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "creat_time")
    @ApiModelProperty(value="创建时间")
    private Date creatTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value="修改人id")
    private Long updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value="修改人姓名")
    private String updateName;

    /**
     * 0 正常，1 删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value="0 正常，1 删除")
    private Integer isEffect;



    @TableField(exist = false)
    @ApiModelProperty(value="模块下的子类型")
    private List<BusinessFlowModule> subCategoryList;
}